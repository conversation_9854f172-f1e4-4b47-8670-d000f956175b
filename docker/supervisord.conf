[supervisord]
logfile=/var/log/supervisord.log ; (main log file;default $CWD/supervisord.log)
logfile_maxbytes=50MB        ; (max main logfile bytes b4 rotation;default 50MB)
logfile_backups=10           ; (num of main logfile rotation backups;default 10)
loglevel=info                ; (log level;default info; others: debug,warn,trace)
pidfile=/tmp/supervisord.pid ; (supervisord pidfile;default supervisord.pid)
nodaemon=true               ; (start in foreground if true;default false)
minfds=1024                  ; (min. avail startup file descriptors;default 1024)
minprocs=200                 ; (min. avail process descriptors;default 200)
user=root         
 
[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
autostart=true
autorestart=true
startretries=5
numprocs=1
startsecs=0
process_name=%(program_name)s_%(process_num)02d
stderr_logfile=/var/log/supervisor/%(program_name)s_stderr.log
stderr_logfile_maxbytes=10MB
stdout_logfile=/var/log/supervisor/%(program_name)s_stdout.log
stdout_logfile_maxbytes=10MB
priority=2
 
[program:php-fpm]
command=/usr/sbin/php-fpm -F
autostart=true
autorestart=true
stdout_logfile=/var/log/php-fpm/stdout.log
stdout_logfile_maxbytes=0
stderr_logfile=/var/log/php-fpm/stderr.log
stderr_logfile_maxbytes=0
exitcodes=0
priority=1

[program:init_and_freeradius]
command=freeradius -X
priority=10
autorestart=true

[program:cron]
command=cron -f
autorestart=true
priority=10
