<?php
declare(strict_types=1);

/**
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link      https://cakephp.org CakePHP(tm) Project
 * @since     3.3.0
 * @license   https://opensource.org/licenses/mit-license.php MIT License
 */
namespace App;

use Arra\ArraPlugin;
use Cake\Core\Configure;
use Cake\Core\ContainerInterface;
use Cake\Datasource\FactoryLocator;
use Cake\Error\Middleware\ErrorHandlerMiddleware;
use Cake\Http\BaseApplication;
use Cake\Http\Middleware\BodyParserMiddleware;
use Cake\Http\Middleware\CsrfProtectionMiddleware;
use Cake\Http\MiddlewareQueue;
use Cake\ORM\Locator\TableLocator;
use Cake\Routing\Middleware\AssetMiddleware;
use Cake\Routing\Middleware\RoutingMiddleware;

//-- Authentication Plugin --
use Authentication\AuthenticationService;
use Authentication\AuthenticationServiceInterface;
use Authentication\AuthenticationServiceProviderInterface;
use Authentication\Identifier\IdentifierInterface;
use Authentication\Middleware\AuthenticationMiddleware;
use Cake\Routing\Router;
use Psr\Http\Message\ServerRequestInterface;

#use Authentication\Identifier\LdapIdentifier;
#use Authentication\Identifier\CustomLdapIdentifier;
use App\Authentication\Identifier\CustomTokenIdentifier;


use Cake\ORM\TableRegistry;


/**
 * Application setup class.
 *
 * This defines the bootstrapping logic and middleware layers you
 * want to use in your application.
 */
class Application extends BaseApplication implements AuthenticationServiceProviderInterface
{


    /**
     * Load all the application configuration and bootstrap logic.
     *
     * @return void
     */
    public function bootstrap(): void
    {

    	$this->addPlugin('CsvView');
        // Call parent to load bootstrap from files.
        parent::bootstrap();

        if (PHP_SAPI === 'cli') {
            $this->bootstrapCli();
        } else {
            FactoryLocator::add(
                'Table',
                (new TableLocator())->allowFallbackClass(false)
            );
        }

        /*
         * Only try to load DebugKit in development mode
         * Debug Kit should not be installed on a production system
         */
        if (Configure::read('debug')) {
            $this->addPlugin('DebugKit');
        }

        // Load more plugins here
        //--Jan 2025--
        $this->addPlugin('Authentication');
        $this->addPlugin(ArraPlugin::class);
        //--Mar 2025--
        $this->addPlugin('Authorization');
    }

    /**
     * Setup the middleware queue your application will use.
     *
     * @param \Cake\Http\MiddlewareQueue $middlewareQueue The middleware queue to setup.
     * @return \Cake\Http\MiddlewareQueue The updated middleware queue.
     */
    public function middleware(MiddlewareQueue $middlewareQueue): MiddlewareQueue
    {
        $middlewareQueue
            // Catch any exceptions in the lower layers,
            // and make an error page/response
            ->add(new ErrorHandlerMiddleware(Configure::read('Error')))

            // Handle plugin/theme assets like CakePHP normally does.
            ->add(new AssetMiddleware([
                'cacheTime' => Configure::read('Asset.cacheTime'),
            ]))

            // Add routing middleware.
            // If you have a large number of routes connected, turning on routes
            // caching in production could improve performance. For that when
            // creating the middleware instance specify the cache config name by
            // using it's second constructor argument:
            // `new RoutingMiddleware($this, '_cake_routes_')`
            ->add(new RoutingMiddleware($this))

            // Parse various types of encoded request bodies so that they are
            // available as array through $request->getData()
            // https://book.cakephp.org/4/en/controllers/middleware.html#body-parser-middleware
            ->add(new BodyParserMiddleware())

            // Cross Site Request Forgery (CSRF) Protection Middleware
            // https://book.cakephp.org/4/en/security/csrf.html#cross-site-request-forgery-csrf-middleware
            ////->add(new CsrfProtectionMiddleware([
           ////     'httponly' => true,
           //// ]));

            // Add the AuthenticationMiddleware. It should be
            // after routing and body parser.
            ->add(new AuthenticationMiddleware($this));

        return $middlewareQueue;
    }

    /**
     * Register application container services.
     *
     * @param \Cake\Core\ContainerInterface $container The Container to update.
     * @return void
     * @link https://book.cakephp.org/4/en/development/dependency-injection.html#dependency-injection
     */
    public function services(ContainerInterface $container): void
    {
    }

    /**
     * Bootstrapping for CLI application.
     *
     * That is when running commands.
     *
     * @return void
     */
    protected function bootstrapCli(): void
    {
        $this->addOptionalPlugin('Cake/Repl');
        $this->addOptionalPlugin('Bake');

        $this->addPlugin('Migrations');

        // Load more plugins here
    }

    public function getAuthenticationService(ServerRequestInterface $request): AuthenticationServiceInterface
    {
        $service = new AuthenticationService();

        // Load the FormAuthenticator (or other authenticators)
        $service->loadAuthenticator('Authentication.Form', [
            'fields' => [
                'username' => 'username',
                'password' => 'password',
            ],
            'loginUrl' => '/cake4/rd_cake/dashboard/authenticate.json',
        ]);

        $service->loadIdentifier('Authentication.Password', [
            'fields' => [
                'username' => 'username',
                'password' => 'password',
            ],
            'resolver' => [
                'className' => 'Authentication.Orm',
                'userModel' => 'Users'
            ],
            'passwordHasher' => [
                'className' => 'Authentication.Fallback',
                'hashers' => [
                    'Authentication.Default',
                    [
                        'className' => 'Authentication.Legacy',
                        'hashType' => 'sha1',
                    ],
                ],
            ],
        ]);

        //------- LDAP ------
        $settingsTable  = TableRegistry::getTableLocator()->get('UserSettings');
        $ldapSetting    = $settingsTable->find()->where(['UserSettings.user_id' => -1, 'UserSettings.name' => 'ldap_enabled'])->first();

       if($ldapSetting && $ldapSetting->value == '1'){

            // Load LDAP Identifier
            $service->loadIdentifier('Authentication.CustomLdapIdentifier', [
                'className' => \App\Authentication\Identifier\CustomLdapIdentifier::class,
                'fields' => [
                   'username' => 'username',
                   'password' => 'password'
                ],
                //'host'      => 'localhost',
                //'port'      => 389,
                'bindDN'    => function($username) {
                    return 'uid='.$username.',ou=People,dc=localdomain,dc=com'; //transform into a rdn or dn
                },
                'options'   => [LDAP_OPT_PROTOCOL_VERSION => 3],
                'ldap'      => \App\Authentication\Identifier\Ldap\CustomExtensionAdapter::class,
                //'admin_dn'  => 'cn=admin,dc=localdomain,dc=com',
                //'admin_pw'  => 'testing123'
            ]);

        }
        //------ END LDAP ----

        $service->loadAuthenticator('Authentication.CustomToken', [
            'className' => \App\Authentication\Authenticator\CustomTokenAuthenticator::class,
            'header' => 'Authorization',
            'tokenPrefix' => 'Bearer',
            'queryParam' => 'token',
            'tokenField' => 'token',
            'unauthenticatedRedirect' => null,
            'checkExpired' => true,
        ]);


        // Load the Token Identifier
        $service->loadIdentifier('Authentication.Token', [
            'tokenField' => 'token',
            'dataField' => 'token',
            'resolver' => [
                'className' => 'Authentication.Orm',
                'userModel' => 'Users',
            ],
        ]);

        return $service;
    }

}
