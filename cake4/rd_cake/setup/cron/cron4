#Every five minutes, run the stored procedure to check for 'down' nodes
*/5 * * * * root su - www-data -s /bin/bash -c 'cd /var/www/html/cake4/rd_cake && bin/cake update_node_downtime >> /dev/null 2>&1'

* * * * * root su - www-data -s /bin/bash -c 'cd /var/www/html/cake4/rd_cake && bin/cake rd mon >> /dev/null 2>&1'
* * * * * root su - www-data -s /bin/bash -c 'cd /var/www/html/cake4/rd_cake && bin/cake rd debug_check >> /dev/null 2>&1'
* * * * * root su - www-data -s /bin/bash -c 'cd /var/www/html/cake4/rd_cake && bin/cake rd auto_close >> /dev/null 2>&1'
* * * * * root su - www-data -s /bin/bash -c 'cd /var/www/html/cake4/rd_cake && bin/cake accounting >> /dev/null 2>&1'
*/15 * * * * root su - www-data -s /bin/bash -c 'cd /var/www/html/cake4/rd_cake && bin/cake voucher >> /dev/null 2>&1'
*/10 * * * * root su - www-data -s /bin/bash -c 'cd /var/www/html/cake4/rd_cake && bin/cake update_user_stats >> /dev/null 2>&1'
*/10 * * * * root su - www-data -s /bin/bash -c 'cd /var/www/html/cake4/rd_cake && bin/cake auto_add_devices >> /dev/null 2>&1'
30 2 * * * root su - www-data -s /bin/bash -c 'cd /var/www/html/cake4/rd_cake && bin/cake auto_clean_mesh_desk >> /dev/null 2>&1'
30 * * * * root su - www-data -s /bin/bash -c 'cd /var/www/html/cake4/rd_cake && bin/cake update_nas_data_usage >> /dev/null 2>&1'

#Compacting and daily user_stats
10 3 * * * root su - www-data -s /bin/bash -c 'cd /var/www/html/cake4/rd_cake && bin/cake compacting >> /dev/null 2>&1'
10 4 * * * root su - www-data -s /bin/bash -c 'cd /var/www/html/cake4/rd_cake && bin/cake update_user_stats_dailies >> /dev/null 2>&1'

#Improved reporting for meshes
*/5 * * * * root su - www-data -s /bin/bash -c 'php /var/www/html/cake4/rd_cake/setup/scripts/reporting/process_report.php >> /dev/null 2>&1'

#FUP Run every minute
* * * * * root su - www-data -s /bin/bash -c 'cd /var/www/html/cake4/rd_cake && bin/cake fup >> /dev/null 2>&1'

#OTP Cleanup
*/5 * * * * root su - www-data -s /bin/bash -c 'cd /var/www/html/cake4/rd_cake && bin/cake otp_cleanup >> /dev/null 2>&1'
