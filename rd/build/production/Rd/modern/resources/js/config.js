Rd = {};

Rd.config = {

    //Graph Related
    rdGraphLabel    : {
        fontSize    : '14px',
        fontFamily  : 'Roboto, Arial, san-serif',
        fill        : '#29465b'
    },
    rdGraphBarColors: ['#56b3fa', '#3e8ebe' ],
    rdTextColor     : '#29465b',
    
    //GUI Improvements
    gridPadding     : 10,
    gridSlim        : '0 3 0 3',
    tabAccPrvdrs    : 'tab-blue',
    tabRealms       : 'tab-orange',
    tabPermUsers    : 'tab-blue',
    tabVouchers     : 'tab-orange',
    tabDevices      : 'tab-metal',
    tabDtaUsageCl   : 'tab-metal',
    tabProfileComp  : 'tab-metal',
    tabDynamicClients : 'tab-blue',
    
    btnUiReload     : 'button-orange',
    
    //Site specific lingo
    meshNetworks    : 'NETWORKS',
    meshNodes       : 'NODES',
    meshUsers       : 'CONNECTED USERS',
    meshData        : 'DATA USED',
    meshNodesOnline : 'ONLINE',
    meshNodesOffline : 'OFFLINE',

    headerName      : 'RADIUSdesk',
    footerName      : 'RADIUSDESK',
    footerLicense   : 'Version 21-42-1',
    levelAColor     : '#737373',
    levelBColor     : '#bfbfbf',  
    selLanguage     : '4_4', //4_4 = en_GB
    buttonMargin    : '10 15 10 15',
    btnScale        : 'large',
    btnUiCommon     : 'button-teal',
    btnUiDataNext   : 'button-teal',
    btnUiDataPrev   : 'default',
    btnUiRefresh    : 'button-orange',
    fieldMargin     : 15,
    labelWidth      : 150,
    maxWidth        : 400,
    numWidth		: 30,
    winWidth        : 900,  //We use this for the initial Width and height
    winHeight       : 500,
    gridNumberCol   : 150,
    radioMargin     : 10,
    panelGrey       : '#ebebeb',
    panelGrey2      : '#bfbfbf',
    'icnSignIn'     : 'xf2f6@FontAwesome', //f2f6 (sign-in-alt) old f090
    'icnSignOut'    : 'xf2f5@FontAwesome', //f2f5 (sign-out-alt)old f08b
    'icnLock'       : 'xf023@FontAwesome', //f023 (lock)
    'icnYes'        : 'xf00c@FontAwesome',
    'icnMenu'       : 'xf0c9@FontAwesome',
    'icnInfo'       : 'xf129@FontAwesome',
    'icnPower'      : 'xf011@FontAwesome',
    'icnSpanner'    : 'xf0ad@FontAwesome',
    'icnHome'       : 'xf015@FontAwesome',
    'icnDynamic'    : 'xf0d0@FontAwesome',
    'icnVoucher'    : 'xf145@FontAwesome',
    'icnNext'       : 'xf061@FontAwesome',
    'icnBack'       : 'xf060@FontAwesome',
    'icnReload'     : 'xf021@FontAwesome',
    'icnAdd'        : 'xf067@FontAwesome',
    'icnEdit'       : 'xf040@FontAwesome',
    'icnDelete'     : 'xf1f8@FontAwesome',
    'icnPdf'        : 'xf1c1@FontAwesome',
    'icnCsv'        : 'xf1c3@FontAwesome',
    'icnRadius'     : 'xf1ce@FontAwesome',
    'icnLight'      : 'xf204@FontAwesome',
    'icnNote'       : 'xf08d@FontAwesome',
    'icnKey'        : 'xf084@FontAwesome',
    'icnRealm'      : 'xf06c@FontAwesome', //make it a leaf
    'icnNas'        : 'xf1cb@FontAwesome',
    'icnTag'        : 'xf02b@FontAwesome',
    'icnProfile'    : 'xf1b3@FontAwesome',
    'icnComponent'  : 'xf12e@FontAwesome',
    'icnActivity'   : 'xf0e7@FontAwesome',
    'icnLog'        : 'xf044@FontAwesome',
    'icnTranslate'  : 'xf0ac@FontAwesome',
    'icnConfigure'  : 'xf0ad@FontAwesome',
    'icnUser'       : 'xf007@FontAwesome',
    'icnDevice'     : 'xf10a@FontAwesome',
    'icnMesh'       : 'xf20e@FontAwesome',
    'icnBug'        : 'xf188@FontAwesome',
    'icnMobile'     : 'xf10b@FontAwesome',
    'icnDesktop'    : 'xf108@FontAwesome',
    'icnView'       : 'xf002@FontAwesome',
    'icnMeta'       : 'xf0cb@FontAwesome',
    'icnMap'        : 'xf041@FontAwesome',
    'icnConnect'    : 'xf0c1@FontAwesome',
    'icnGraph'      : 'xf080@FontAwesome',
    'icnKick'       : 'xf1e6@FontAwesome',
    'icnClose'      : 'xf00d@FontAwesome',
    'icnFinance'    : 'xf09d@FontAwesome',
    'icnOnlineShop' : 'xf07a@FontAwesome',
    'icnEmail'      : 'xf0e0@FontAwesome',
    'icnAttach'     : 'xf0c6@FontAwesome',
    'icnCut'        : 'xf0c4@FontAwesome',
    'icnTopUp'      : 'xf0f4@FontAwesome',
    'icnSubtract'   : 'xf068@FontAwesome',
    'icnWatch'      : 'xf017@FontAwesome',
    'icnStar'       : 'xf005@FontAwesome',
    'icnGrid'       : 'xf00a@FontAwesome',
    'icnFacebook'   : 'xf082@FontAwesome',
    'icnGoogle'     : 'xf1a0@FontAwesome',
    'icnTwitter'    : 'xf099@FontAwesome',
    'icnWifi'       : 'xf012@FontAwesome',
    'icnIP'         : 'xf1c0@FontAwesome',
    'icnThumbUp'    : 'xf087@FontAwesome',
    'icnThumbDown'  : 'xf088@FontAwesome',
    'icnCPU'        : 'xf085@FontAwesome',
    'icnCamera'     : 'xf030@FontAwesome',
    'icnFolder'     : 'xf07b@FontAwesome',
    'icnSnapshot'   : 'xf030@FontAwesome',
    'icnTime'       : 'xf017@FontAwesome',
    'icnExpand'     : 'xf065@FontAwesome',
    'icnOperator'   : 'xf19d@FontAwesome',
    'icnRadiusClient': 'xf1ce@FontAwesome',
    'icnHeart'      : 'xf004@FontAwesome',    
    'icnGear'       : 'xf013@FontAwesome',
    'icnGears'      : 'xf085@FontAwesome',
    'icnSite'       : 'xf19c@FontAwesome',
    'icnSsid'       : 'xf1eb@FontAwesome',
    'icnOverview'   : 'xf015@FontAwesome',
    'icnData'       : 'xf1c0@FontAwesome',
    'icnAccessPoint': 'xf0b2@FontAwesome',
    'icnChain'      : 'xf0c1@FontAwesome',
    'icnChainBroken': 'xf127@FontAwesome',
    'icnNetwork'    : 'xf1e0@FontAwesome',
    'icnAngleLeft'  : 'xf100@FontAwesome',
    'icnAngleRight' : 'xf101@FontAwesome',
    'icnRedirect'   : 'xf074@FontAwesome',
    'icnClear'      : 'xf0c4@FontAwesome',
    'icnStart'      : 'xf05d@FontAwesome',
    'icnStop'       : 'xf00d@FontAwesome',
    'icnInfo'       : 'xf033@FontAwesome',
    'icnCopy'       : 'xf0c5@FontAwesome',
    'icnDynamicNas' : 'xf239@FontAwesome',
    'icnQuestion'   : 'xf059@FontAwesome',
    'icnCheck'      : 'xf058@FontAwesome',
    'icnCloud'      : 'xf0c2@FontAwesome',
    'icnMeshEdit'   : 'xf044@FontAwesome',
    'icnWizard'     : 'xf0d0@FontAwesome',
    'icnVPN'        : 'xf10e@FontAwesome',
    'icnAdmin'      : 'xf19d@FontAwesome',
    'icnBan'        : 'xf05e@FontAwesome',
    'icnHourStart'  : 'xf251@FontAwesome',
    'icnHourHalf'   : 'xf252@FontAwesome',
    'icnHourEnd'    : 'xf253@FontAwesome',
    'icnBullhorn'   : 'xf0a1@FontAwesome',
    'icnList'       : 'xf03a@FontAwesome',
    'icnFilter'     : 'xf0b0@FontAwesome',
    'icnDropbox'    : 'xf16b@FontAwesome',
    'icnHistory'    : 'xf1da@FontAwesome',
    'icnGroup'      : 'xf0c0@FontAwesome',
    'icnDash'       : 'xf1fe@FontAwesome',
    'icnPlusCircle' : 'xf055@FontAwesome',
    'icnExclCircle' : 'xf06a@FontAwesome',
    'icnCube'       : 'xf1b2@FontAwesome',
    'icnBell'       : 'xf0f3@FontAwesome',
    'icnScale'      : 'xf24e@FontAwesome',
    'icnEyeSlash'   : 'xf070@FontAwesome',
    'icnLightbulb'  : 'xf0eb@FontAwesome',
    'icnSpeed'      : 'xf0e4@FontAwesome',
    'icnLeft'       : 'xf104@FontAwesome',
    'icnRight'      : 'xf105@FontAwesome',
    'icnRecycle'    : 'xf1b8@FontAwesome',
    'icnHands'      : 'xf255@FontAwesome',
    'icnDotCircleO' : 'xf192@FontAwesome',
    'icnGlobe'      : 'xf0ac@FontAwesome',
    'icnTable'      : 'xf0ce@FontAwesome',
    'icnSkyatlas'   : 'xf216@FontAwesome',
    'icnAndroid'    : 'xf17b@FontAwesome',
    'icnApple'      : 'xf179@FontAwesome',
    'icnSignIn'     : 'xf090@FontAwesome',
    'icnGavel'      : 'xf0e3@FontAwesome',
    'icnFlag'       : 'xf024@FontAwesome',
    'icnCertificate': 'xf0a3@FontAwesome',
    'icnArrows'     : 'xf047@FontAwesome',
    'icnExchange'   : 'xf0ec@FontAwesome',
    'icnHeartbeat'  : 'xf21e@FontAwesome',
    'icnThermometer': 'xf2c9@FontAwesome',
    'icnListOl'     : 'xf0cb@FontAwesome',
    'icnHandshakeO' : 'xf2b5@FontAwesome',
    'icnHddO'       : 'xf0a0@FontAwesome',
    'icnExchange'   : 'xf0ec@FontAwesome',
    'icnFire'       : 'xf06d@FontAwesome',
    'icnCalendar'	: 'xf073@FontAwesome',
    'icnBus'		: 'xf207@FontAwesome',
    'icnMeter'		: 'xf0e4@FontAwesome',
    'icnQrcode'     : 'xf029@FontAwesome',
    'icnUpoad'      : 'xf093@FontAwesome',
    'icnSitemap'    : 'xf0e8@FontAwesome',
    'icnPlug'       : 'xf1e6@FontAwesome',
    'icnToggleOn'   : 'xf205@FontAwesome',
    'icnScale'      : 'xf24e@FontAwesome',
    'icnPauseO'     : 'xf28c@FontAwesome',
    'icnPlayO'      : 'xf01d@FontAwesome',
    'icnSliders'    : 'xf1de@FontAwesome',
    'icnEye'        : 'xf06e@FontAwesome',
    'icnPlay'       : 'xf04b@FontAwesome'
}

