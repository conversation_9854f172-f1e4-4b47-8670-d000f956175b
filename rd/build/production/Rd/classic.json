{"packages": {"charts": {"css": true, "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "classic"], "theme": "theme-classic", "version": "7.0.0"}, "classic": {"css": true, "included": true, "language": {"js": {"input": {"version": "ES5"}}}, "namespace": "Ext", "required": true, "requires": ["ext", "core"], "version": "7.0.0"}, "cmd": {"version": "7.5.1.20"}, "core": {"css": true, "included": true, "required": true, "requires": ["ext", "classic"], "version": "7.0.0"}, "ext": {"css": true, "included": true, "language": {"js": {"input": {"version": "ES5"}}}, "license": "dev", "namespace": "Ext", "required": true, "requires": [], "version": "7.0.0.156"}, "font-awesome": {"css": true, "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "classic", "theme-base", "theme-neutral", "theme-neptune"], "theme": "theme-neptune", "version": "5.6.3"}, "font-ext": {"css": true, "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "classic", "theme-base", "theme-neutral", "theme-neptune"], "theme": "theme-neptune", "version": "7.0.0"}, "rd-theme": {"css": true, "extend": "theme-triton", "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "classic", "theme-base", "theme-neutral", "theme-neptune", "font-awesome", "font-ext", "theme-triton"], "version": "1.0.0"}, "theme-base": {"css": true, "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "classic"], "version": "7.0.0"}, "theme-neptune": {"css": true, "extend": "theme-neutral", "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "classic", "theme-base", "theme-neutral"], "version": "7.0.0"}, "theme-neutral": {"css": true, "extend": "theme-base", "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "classic", "theme-base"], "version": "7.0.0"}, "theme-triton": {"css": true, "extend": "theme-neptune", "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "classic", "theme-base", "theme-neutral", "theme-neptune", "font-awesome", "font-ext"], "version": "7.0.0"}, "ux": {"css": true, "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "classic"], "theme": "theme-classic", "version": "7.0.0"}}, "js": [{"path": "classic/app.js"}, {"path": "resources/js/singletons.js"}, {"path": "resources/js/config.js"}, {"path": "resources/js/jit-yc.js"}], "css": [{"remote": false, "exclude": ["fashion"], "path": "classic/resources/Rd-all_1.css"}, {"remote": false, "exclude": ["fashion"], "path": "classic/resources/Rd-all_2.css"}, {"remote": false, "exclude": ["fashion"], "path": "classic/resources/Rd-all_3.css"}, {"remote": false, "exclude": ["fashion"], "path": "classic/resources/Rd-all_4.css"}], "cache": {"enable": true, "deltas": "classic/deltas"}, "fashion": {"inliner": {"enable": false}, "missingParameters": "error"}, "name": "Rd", "namespace": "Rd", "version": "1.0.0.0", "framework": "ext", "loader": {"cache": "20250720153834", "cacheParam": "_dc"}, "id": "80083863-03ef-4613-b411-df3986e04a19", "toolkit": "classic", "theme": "rd-theme", "hash": "189acf0224a65f35febe40699f229e89e2b32ca0", "profile": "classic", "appCacheEnabled": true, "resources": {"path": "classic/resources", "shared": "resources"}}