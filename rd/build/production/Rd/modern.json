{"packages": {"charts": {"css": true, "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "modern"], "version": "7.0.0"}, "cmd": {"version": "7.5.1.20"}, "core": {"css": true, "included": true, "required": true, "requires": ["ext", "modern"], "version": "7.0.0"}, "ext": {"css": true, "included": true, "language": {"js": {"input": {"version": "ES5"}}}, "license": "dev", "namespace": "Ext", "required": true, "requires": [], "version": "7.0.0.156"}, "font-awesome": {"css": true, "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "modern", "theme-base", "font-ext", "theme-neptune"], "theme": "theme-neptune", "version": "5.6.3"}, "font-ext": {"css": true, "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "modern", "theme-base", "font-awesome", "theme-neptune"], "theme": "theme-neptune", "version": "7.0.0"}, "modern": {"css": true, "included": true, "language": {"js": {"input": {"version": "ES5"}}}, "namespace": "Ext", "required": true, "requires": ["ext", "core"], "version": "7.0.0"}, "theme-base": {"css": true, "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "modern"], "version": "7.0.0"}, "theme-material": {"css": true, "extend": "theme-neptune", "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "modern", "theme-base", "font-awesome", "font-ext", "theme-neptune"], "version": "7.0.0"}, "theme-neptune": {"css": true, "extend": "theme-base", "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "modern", "theme-base", "font-awesome", "font-ext"], "version": "7.0.0"}, "ux": {"css": true, "included": true, "namespace": "Ext", "required": true, "requires": ["ext", "core", "modern"], "version": "7.0.0"}}, "js": [{"path": "modern/app.js"}, {"path": "resources/js/singletons.js"}, {"path": "resources/js/config.js"}, {"path": "resources/js/jit-yc.js"}], "css": [{"exclude": ["fashion"], "path": "modern/resources/Rd-all.css"}], "cache": {"enable": true, "deltas": "modern/deltas"}, "fashion": {"inliner": {"enable": false}, "missingParameters": "error"}, "name": "Rd", "namespace": "Rd", "version": "1.0.0.0", "framework": "ext", "loader": {"cache": "20250720154001", "cacheParam": "_dc"}, "id": "80083863-03ef-4613-b411-df3986e04a19", "toolkit": "modern", "theme": "theme-material", "hash": "4a88277f06178cf23d1df6ba98be74359a76e9bd", "profile": "modern", "appCacheEnabled": true, "resources": {"path": "modern/resources", "shared": "resources"}}