
/*  App Loading
--------------- */
.spinner {
  margin: 100px auto;
  width: 50px;
  height: 30px;
  text-align: center;
  font-size: 10px;
}

.spinner > div {
  background-color: #1861b5;
  height: 100%;
  width: 6px;
  display: inline-block;
  
  -webkit-animation: stretchdelay 1.2s infinite ease-in-out;
  animation: stretchdelay 1.2s infinite ease-in-out;
}

.spinner .rect2 {
  -webkit-animation-delay: -1.1s;
  animation-delay: -1.1s;
}

.spinner .rect3 {
  -webkit-animation-delay: -1.0s;
  animation-delay: -1.0s;
}

.spinner .rect4 {
  -webkit-animation-delay: -0.9s;
  animation-delay: -0.9s;
}

.spinner .rect5 {
  -webkit-animation-delay: -0.8s;
  animation-delay: -0.8s;
}

@-webkit-keyframes stretchdelay {
  0%, 40%, 100% { -webkit-transform: scaleY(0.4) }  
  20% { -webkit-transform: scaleY(1.0) }
}

@keyframes stretchdelay {
  0%, 40%, 100% { transform: scaleY(0.4) }  
  20% { transform: scaleY(1.0) }
}
.loading-indicator {
	color: #1861b5;
	text-align: center;
	font-size: 34px;
    font-stretch: ultra-expanded;
    line-height: 16px;
    font-family: "Open Sans", "Helvetica Neue", helvetica, arial, verdana, sans-serif;
}


/*

.dataview-item {
    display: flex;
    padding: 10px;
    border-bottom: 1px solid #ccc;
}

.dataview-column1, .dataview-column2 {
    flex: 1;
    padding: 10px;
    cursor: pointer;
}

.dataview-column1:hover, .dataview-column2:hover {
    background-color: #f0f0f0;
}

.dataview-column1.selected, .dataview-column2.selected {
    background-color: #d0e5f5;
}
*/

.dataview-columns {
    display: flex;
    flex-wrap: wrap;
}

.dataview-column {
    flex-basis: 50%;
}


.dataview-item {
    display: flex;
    flex-direction: column;
    padding: 15px;
    margin: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.dataview-field {
    margin: 5px 0;
    font-size: 18px;
    font-stretch: expanded;
    font-weight: 600;
    color: #333;
}

.dataview-field label {
    margin-right: 5px;
    font-size: 16px;
    color: #666;
    font-weight: 100;
}

.dataview-item:hover {
    background-color: #f9f9f9;
    border-color: #007bff;
}

.dataview-item.selected {
    background-color: #e9f5ff;
    border-color: #007bff;
}

/* Define custom styles for selected items in the DataView */
.custom-dataview .x-item-selected {
    background-color: #337ab7; /* Change background color */
    color: white; /* Change text color */
    border: 2px solid #2e6da4; /* Add a border */
    border-radius: 4px; /* Add rounded corners */
    padding: 5px; /* Add some padding */
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); /* Add a shadow effect */
}

/* Additional styles for hover state (optional) */
.custom-dataview .x-item-hover {
    background-color: #5bc0de; /* Change background color on hover */
    color: white; /* Change text color on hover */
    cursor: pointer; /* Change cursor to pointer */
}

.custom-dataview  .dataview-extra {
    color: #385a6b;
    font-stretch: expanded;
    font-weight: 100;
}

.custom-dataview .x-item-selected .dataview-extra {
    color: white;
}

.custom-dataview .x-item-selected .dataview-field label {
    color: white;
}

.custom-dataview .x-item-selected .dataview-field {
    color: white;
}

.parent-div {
  display: flex; /* or inline-flex */
}

.sub-div-1, .sub-div-2 {
  flex: 1; /* or width: 50% */
}

.rdCombo .x-form-text-default {
    color: #29465b; /* Change text color */
    font-size: 18px; /* Change text size */
    background-color: #b6b7b982;
}

.split-dataview-item {
    display: flex;
    padding: 10px;
    border-bottom: 1px solid #ccc;
    font-size: 16px;
    color: #124765;
}

.split-dataview-column1, .split-dataview-column2 {
    flex: 1;
    padding: 10px;
    cursor: pointer;
}

.split-dataview-column1:hover, .split-dataview-column2:hover {
    background-color: #f0f0f0;
}

.split-dataview-column1.selected, .split-dataview-column2.selected {
    background-color: #d0e5f5;
}


.reminder-message {
    background-color: #fffbe6; /* Soft yellow background for visibility */
    color: #856404; /* Dark gold text for contrast */
    border: 1px solid #ffeeba; /* Light gold border */
    padding: 5px;
    font-size: 0.9em;
    margin: 5px;
}

.reminder-message .icon {
    font-size: 1.2em;
    color: #856404; /* Matches the text color */
    margin-right: 8px;
}

.heading {
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #ccc;
    color: #03861e; 
}

.lblContainer {
    display: flex;
    align-items: center; /* Align items vertically */
    margin: 1px 0; /* Add spacing between rows */
}

.lblItem {
    width: 120px; /* Fixed width for alignment */
    text-align: left;
    color: #868686;
    font-size: 14px;
    margin-right: 10px; /* Spacing between label and value */
}

.lblValue {
    flex-grow: 1; /* Allow the value label to take remaining space */
    text-align: left;
    font-size: 14px;
    color: #005691;
}

.divGroup {
    background-color: #dcedf7;
    border: 1px solid #ccc;
    transition: background-color 0.3s, box-shadow 0.3s;
}
.divGroup:hover {
    background-color: #b3e5fc; /* Slightly brighter shade */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Slightly stronger shadow */
}

.banner-container { 
  color: #29495b;
  background :linear-gradient(135deg, #e6f0ff, #cce0ff, #99ccff);
  box-shadow : 0 2px 5px rgba(0, 0, 0, 0.1);
  padding : 5px;
}


.speedtest-stats-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.speedtest-stat {
    flex: 1;
    text-align: center;
    padding: 10px;
}

.speedtest-value {
    font-size: 24px;
    font-weight: bold;
}

.speedtest-label {
    font-size: 14px;
    color: gray;
    margin-top: 5px;
}

.speedtest-secondary {
    margin-top: 15px;
    padding: 10px;
    background-color: #f4f4f4;
    border-radius: 8px;
    font-size: 13px;
    color: #333;
    line-height: 1.6;
    box-shadow: 0 0 4px rgba(0,0,0,0.1);
}

.speedtest-secondary-item {
    margin-bottom: 6px;
}

.banner-link i {
    font-style: normal !important;
}

.banner-c {
    background: linear-gradient(to right, #f5f7fa, #c3cfe2);
    border-bottom: 1px solid #ccc;
    padding : 5px;
}

.banner-link {
    margin: 0 50px;
    text-align: center;
    cursor: pointer;
    font-size: 14px;
    color: #3f474f;
}

.banner-link:hover {
    color: #03a3a6;
}

.banner-link.selected {
    color: #0056b3;
    border-bottom: 2px solid #0056b3;
}

.connector-line {
    border-bottom: 2px dashed #888;
    margin-top: 20px;
}

.banner-link:hover::after {
    border-color: #0056b3;
}

